'use client'

import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { IconUsed, OrderPickupInfo, OrderVouchers } from '@ninebot/core'
import { Carousel } from 'antd'
import { CarouselRef } from 'antd/es/carousel'

import { CustomImage, IconArrow, Modal } from '@/components'

// 扩展券码类型，增加使用门店和使用时间
// interface ExtendedVoucher {
//   store_name?: string
//   used_time?: string
//   code?: string | null
//   qr_code?: string | null
//   status?: string | null
//   status_label?: string | null
//   expired_at?: string
// }

type CouponPopupProps = {
  /** 是否显示 */
  popupVisible: boolean
  /** 拷贝到剪贴板 */
  copyToClipboard: (text: string) => void
  /** 关闭弹窗 */
  closePopup: () => void
  /** 自提取货码 */
  pickupInfo?: OrderPickupInfo
  couponList?: OrderVouchers
  /** 商品信息 */
  productInfo?: {
    name: string
    image: string
  }
}

/**
 * 券码弹窗
 */
const CouponPopup = (props: CouponPopupProps) => {
  const {
    popupVisible,
    copyToClipboard,
    closePopup,
    pickupInfo,
    couponList = [],
    productInfo,
  } = props
  const getI18nString = useTranslations('Common')
  const swiperRef = useRef<CarouselRef>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  /**
   * 判断当前是否为取货码模式
   */
  const isPickupCode = useMemo(() => {
    return !!pickupInfo
  }, [pickupInfo])

  /**
   * 获取当前显示的验证码
   */
  const currentCode = useMemo(() => {
    return isPickupCode ? pickupInfo?.code : couponList?.[currentIndex]?.code
  }, [isPickupCode, pickupInfo?.code, couponList, currentIndex])

  /**
   * 格式化 data
   */
  const carouselData = useMemo(() => {
    if (pickupInfo) {
      return [
        {
          image_url: pickupInfo?.qr_code,
          imageStyle: pickupInfo?.status === '0' ? {} : { opacity: 0.08 },
          children:
            pickupInfo?.status !== '0' ? (
              <div className="absolute bottom-[8px] right-[8px]">
                <IconUsed />
                <span className="absolute bottom-[24px] right-[8px] -rotate-[40deg] text-[20px] text-[#86868B]">
                  {pickupInfo?.status_label}
                </span>
              </div>
            ) : null,
        },
      ]
    } else {
      return couponList?.map((coupon) => {
        return {
          image_url: coupon?.qr_code,
          imageStyle: coupon?.status === '0' ? {} : { opacity: 0.08 },
          children:
            coupon?.status !== '0' ? (
              <div className="absolute bottom-[8px] right-[8px]">
                <IconUsed />
                <span className="absolute bottom-[24px] right-[8px] -rotate-[40deg] text-[20px] text-[#86868B]">
                  {coupon?.status_label}
                </span>
              </div>
            ) : null,
        }
      })
    }
  }, [pickupInfo, couponList])

  /**
   * 获取当前验证码的状态
   */
  const isUnUsed = useMemo(() => {
    if (isPickupCode) {
      return pickupInfo?.status === '0'
    }
    return couponList?.[currentIndex]?.status === '0'
  }, [isPickupCode, pickupInfo?.status, couponList, currentIndex])

  /**
   * 获取当前券码
   */
  // const getCurrentCoupon = useMemo((): ExtendedVoucher | undefined => {
  //   if (isPickupCode) {
  //     return pickupInfo as unknown as ExtendedVoucher
  //   } else {
  //     return couponList?.[currentIndex] as unknown as ExtendedVoucher
  //   }
  // }, [isPickupCode, pickupInfo, couponList, currentIndex])

  /**
   * 处理轮播图切换动画
   */
  const handleSwipeAnimation = () => {
    setIsAnimating(true)
    setTimeout(() => setIsAnimating(false), 300) // 动画结束后重置状态
  }

  /**
   * 切换到上一张
   */
  const handlePrevPress = () => {
    if (currentIndex > 0 && !isAnimating) {
      try {
        const newIndex = Math.max(currentIndex - 1, 0)
        setCurrentIndex(newIndex)
        swiperRef.current?.goTo(newIndex, false)
        handleSwipeAnimation()
      } catch (error) {
        console.error('切换上一张失败:', error)
      }
    }
  }

  /**
   * 切换到下一张
   */
  const handleNextPress = () => {
    if (currentIndex < (couponList?.length || 0) - 1 && !isAnimating) {
      try {
        const newIndex = Math.min(currentIndex + 1, (couponList?.length || 0) - 1)
        setCurrentIndex(newIndex)
        swiperRef.current?.goTo(newIndex, false)
        handleSwipeAnimation()
      } catch (error) {
        console.error('切换下一张失败:', error)
      }
    }
  }

  useEffect(() => {
    if (!popupVisible) {
      swiperRef.current?.goTo(0)
      setCurrentIndex(0)
    }
  }, [popupVisible])

  return (
    <Modal
      isOpen={popupVisible}
      width={600}
      onClose={closePopup}
      okButtonProps={{ style: { display: 'none' } }}
      cancelButtonProps={{ style: { display: 'none' } }}
      title={getI18nString('coupon_code')}>
      <>
        <div className="-mt-base-16 mb-base-16 h-[1px] w-full bg-[#F3F3F4]"></div>

        <div className="mx-auto w-[420px]">
          {productInfo && (
            <div className="mb-[16px] flex items-center justify-between">
              <CustomImage
                width={60}
                height={60}
                className="mr-[16px] rounded-[8px]"
                src={productInfo.image}
                alt={productInfo.name}
              />
              <div className="flex-1 font-miSansDemiBold450 text-[16px] leading-[22px] text-[#0F0F0F]">
                上门/取车维修免费{isPickupCode ? '1' : couponList?.length}次
              </div>

              <div className="text-[16px] leading-[1.4] text-[#000000]">
                {getI18nString('verification_code_total')}
                <span className="font-miSansDemiBold450 text-[20px] text-primary">
                  {isPickupCode ? '1' : couponList?.length}
                </span>
                {getI18nString('piece')}
              </div>
            </div>
          )}

          <div className="relative flex flex-col items-center rounded-base-12 bg-gray-base">
            <div className="relative w-full py-[16px]">
              <div className="absolute -bottom-[8px] -left-[8px] h-[16px] w-[16px] rounded-full bg-white" />
              <div className="absolute -bottom-[8px] -right-[8px] h-[16px] w-[16px] rounded-full bg-white" />
              <div className="flex items-center">
                <div className="min-w-[76px] px-[12px] py-[18px] text-center text-[14px] leading-[20px] text-[#0F0F0F]">
                  {getI18nString('verification_code_index', { key: currentIndex + 1 })}
                </div>
                <div className="h-[56px] w-[1px] border-l border-dashed border-[#E1E1E4] bg-transparent" />
                <div className="flex flex-1 items-center justify-between px-8">
                  <div className="flex flex-col gap-base">
                    <div className="font-miSansDemiBold450 text-[20px] leading-[1.4] text-[#0F0F0F]">
                      {currentCode}
                    </div>
                    {!isPickupCode ? (
                      <div className="font-miSansRegular330 text-[12px] leading-[16px] text-[#6E6E73]">
                        {getI18nString('expired_time_until')}
                        {couponList?.[currentIndex]?.expired_at?.split(' ')[0]}
                      </div>
                    ) : null}
                  </div>
                  {isUnUsed && (
                    <button
                      className="flex h-[32px] items-center justify-center rounded-full border border-primary px-base-16"
                      onClick={() => copyToClipboard(currentCode || '')}>
                      <div className="font-miSansRegular330 text-[14px] leading-[20px] text-primary">
                        {getI18nString('copy')}
                      </div>
                    </button>
                  )}
                </div>
              </div>
            </div>

            <div className="h-[1px] w-[381px] border-b border-dashed border-[#E1E1E4] bg-transparent" />

            <div className="mb-[32px] mt-[24px] w-full flex-1">
              <div className="mx-auto h-[180px] w-[180px]">
                <Carousel
                  ref={swiperRef}
                  dots={false}
                  beforeChange={(from, to) => {
                    console.log('轮播即将从', from, '切换到', to)
                    setCurrentIndex(to)
                  }}
                  afterChange={(current) => {
                    console.log('轮播已切换到', current)
                    setCurrentIndex(current)
                  }}>
                  {carouselData?.map((coupon, index) => {
                    // 获取当前券码的状态
                    const currentCouponStatus = isPickupCode
                      ? pickupInfo?.status
                      : couponList?.[index]?.status
                    const isCurrentUnused = currentCouponStatus === '0'

                    return (
                      <div key={index} className="relative">
                        <CustomImage
                          displayMode="responsive"
                          src={coupon.image_url || ''}
                          className={`h-full w-full ${!isCurrentUnused ? 'opacity-[0.08]' : ''}`}
                          style={{ ...coupon.imageStyle }}
                          alt=""
                        />
                        {coupon.children}
                      </div>
                    )
                  })}
                </Carousel>
              </div>
            </div>

            {Number(carouselData?.length) > 1 ? (
              <button
                className="absolute -left-[26px] top-1/2 flex h-16 w-16 -translate-x-full -translate-y-1/2 items-center justify-center rounded-full bg-[#00000014]"
                onClick={handlePrevPress}>
                <IconArrow size={20} rotate={90} />
              </button>
            ) : null}

            {Number(carouselData?.length) > 1 ? (
              <button
                className="absolute -right-[26px] top-1/2 flex h-16 w-16 -translate-y-1/2 translate-x-full items-center justify-center rounded-full bg-[#00000014]"
                onClick={handleNextPress}>
                <IconArrow size={20} rotate={-90} />
              </button>
            ) : null}

            <div className="absolute -bottom-base-16 left-0 right-0 translate-y-full text-center text-[16px] leading-[1.4] text-[#000000]">
              {isPickupCode
                ? `${pickupInfo?.expired_info}`
                : couponList && couponList.length > 1
                  ? `${currentIndex + 1}/${couponList?.length}`
                  : null}
            </div>
          </div>
        </div>
      </>
    </Modal>
  )
}

export default CouponPopup
